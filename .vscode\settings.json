{"python.defaultInterpreterPath": "./cifar_env/bin/python", "python.terminal.activateEnvironment": true, "python.formatting.provider": "black", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": true, "python.linting.lintOnSave": true, "jupyter.askForKernelRestart": false, "jupyter.alwaysTrustNotebooks": true, "files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 1000, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}, "python.analysis.typeCheckingMode": "basic", "terminal.integrated.defaultProfile.windows": "Command Prompt", "python.terminal.activateEnvInCurrentTerminal": true}