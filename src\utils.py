import torch
import torchvision
import matplotlib.pyplot as plt
import numpy as np
import os
import json
from datetime import datetime

def get_device():
    """Get the best available device"""
    if torch.cuda.is_available():
        device = torch.device('cuda')
        print(f"GPU available: {torch.cuda.get_device_name(0)}")
    else:
        device = torch.device('cpu')
        print("Using CPU")
    return device

def save_checkpoint(model, optimizer, epoch, loss, filepath):
    """Save model checkpoint"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss,
        'timestamp': datetime.now().isoformat()
    }
    
    os.makedirs(os.path.dirname(filepath), exist_ok=True)
    torch.save(checkpoint, filepath)
    print(f"Checkpoint saved: {filepath}")

def load_checkpoint(filepath, model, optimizer=None):
    """Load model checkpoint"""
    checkpoint = torch.load(filepath, map_location=get_device())
    
    model.load_state_dict(checkpoint['model_state_dict'])
    
    if optimizer and 'optimizer_state_dict' in checkpoint:
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    epoch = checkpoint.get('epoch', 0)
    loss = checkpoint.get('loss', 0)
    
    print(f"Checkpoint loaded: {filepath}")
    print(f"Epoch: {epoch}, Loss: {loss}")
    
    return epoch, loss

def visualize_model_predictions(model, test_loader, class_names, device, num_images=8):
    """Visualize model predictions on test images"""
    model.eval()
    
    # Get a batch of test images
    dataiter = iter(test_loader)
    images, labels = next(dataiter)
    images = images.to(device)
    
    # Make predictions
    with torch.no_grad():
        outputs = model(images)
        probabilities = torch.softmax(outputs, dim=1)
        _, predictions = torch.max(outputs, 1)
        confidences = torch.max(probabilities, 1)[0]
    
    # Denormalize images for display
    def denormalize(tensor):
        tensor = tensor.clone()
        tensor[0] = tensor[0] * 0.2023 + 0.4914
        tensor[1] = tensor[1] * 0.1994 + 0.4822
        tensor[2] = tensor[2] * 0.2010 + 0.4465
        return torch.clamp(tensor, 0, 1)
    
    # Plot predictions
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    for i in range(num_images):
        row, col = i // 4, i % 4
        
        img = denormalize(images[i].cpu())
        axes[row, col].imshow(np.transpose(img, (1, 2, 0)))
        
        true_label = class_names[labels[i]]
        pred_label = class_names[predictions[i].cpu()]
        confidence = confidences[i].cpu().item()
        
        color = 'green' if predictions[i] == labels[i] else 'red'
        
        axes[row, col].set_title(
            f'True: {true_label}\nPred: {pred_label}\nConf: {confidence:.3f}',
            color=color, fontweight='bold'
        )
        axes[row, col].axis('off')
    
    plt.tight_layout()
    plt.savefig('./results/plots/model_predictions.png', dpi=300, bbox_inches='tight')
    plt.show()

def compare_models(results_dict):
    """Compare multiple model results"""
    
    models = list(results_dict.keys())
    metrics = ['overall_accuracy', 'macro_f1', 'weighted_f1']
    
    # Prepare data for plotting
    metric_values = {metric: [] for metric in metrics}
    
    for model in models:
        for metric in metrics:
            metric_values[metric].append(results_dict[model][metric])
    
    # Create comparison plot
    x = np.arange(len(models))
    width = 0.25
    
    fig, ax = plt.subplots(figsize=(12, 6))
    
    for i, metric in enumerate(metrics):
        ax.bar(x + i * width, metric_values[metric], width, 
               label=metric.replace('_', ' ').title())
    
    ax.set_xlabel('Models')
    ax.set_ylabel('Score')
    ax.set_title('Model Comparison')
    ax.set_xticks(x + width)
    ax.set_xticklabels(models)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('./results/plots/model_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def calculate_model_size(model):
    """Calculate model size in MB"""
    param_size = 0
    param_sum = 0
    
    for param in model.parameters():
        param_size += param.nelement() * param.element_size()
        param_sum += param.nelement()
    
    buffer_size = 0
    buffer_sum = 0
    
    for buffer in model.buffers():
        buffer_size += buffer.nelement() * buffer.element_size()
        buffer_sum += buffer.nelement()
    
    size_all_mb = (param_size + buffer_size) / 1024 / 1024
    
    return {
        'parameters': param_sum,
        'size_mb': size_all_mb,
        'param_size': param_size,
        'buffer_size': buffer_size
    }

def analyze_layer_outputs(model, input_tensor, layer_name=None):
    """Analyze intermediate layer outputs"""
    activation = {}
    
    def get_activation(name):
        def hook(model, input, output):
            activation[name] = output.detach()
        return hook
    
    # Register hooks
    for name, layer in model.named_modules():
        if layer_name is None or layer_name in name:
            layer.register_forward_hook(get_activation(name))
    
    # Forward pass
    model.eval()
    with torch.no_grad():
        _ = model(input_tensor)
    
    return activation

def create_learning_curves(history, save_path='./results/plots/learning_curves.png'):
    """Create detailed learning curves"""
    
    epochs = range(1, len(history['train_loss']) + 1)
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # Training and validation loss
    ax1.plot(epochs, history['train_loss'], 'b-', label='Training Loss')
    ax1.plot(epochs, history['val_loss'], 'r-', label='Validation Loss')
    ax1.set_title('Model Loss')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Training and validation accuracy
    ax2.plot(epochs, history['train_acc'], 'b-', label='Training Accuracy')
    ax2.plot(epochs, history['val_acc'], 'r-', label='Validation Accuracy')
    ax2.set_title('Model Accuracy')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Loss difference (overfitting indicator)
    loss_diff = [val - train for val, train in zip(history['val_loss'], history['train_loss'])]
    ax3.plot(epochs, loss_diff, 'g-', label='Val - Train Loss')
    ax3.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax3.set_title('Overfitting Indicator (Loss Difference)')
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Loss Difference')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Accuracy difference
    acc_diff = [train - val for train, val in zip(history['train_acc'], history['val_acc'])]
    ax4.plot(epochs, acc_diff, 'purple', label='Train - Val Accuracy')
    ax4.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax4.set_title('Overfitting Indicator (Accuracy Difference)')
    ax4.set_xlabel('Epoch')
    ax4.set_ylabel('Accuracy Difference')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

def log_experiment_results(experiment_name, config, results, save_path='./results/experiment_log.json'):
    """Log experiment results to a master file"""
    
    # Prepare log entry
    log_entry = {
        'timestamp': datetime.now().isoformat(),
        'experiment_name': experiment_name,
        'config': config,
        'results': results
    }
    
    # Load existing log or create new
    if os.path.exists(save_path):
        with open(save_path, 'r') as f:
            experiment_log = json.load(f)
    else:
        experiment_log = {'experiments': []}
    
    # Add new experiment
    experiment_log['experiments'].append(log_entry)
    
    # Save updated log
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    with open(save_path, 'w') as f:
        json.dump(experiment_log, f, indent=2)
    
    print(f"Experiment logged to: {save_path}")

def generate_model_summary(model, input_size=(3, 32, 32)):
    """Generate detailed model summary"""
    
    def get_model_info(model):
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        return {
            'total_parameters': total_params,
            'trainable_parameters': trainable_params,
            'non_trainable_parameters': total_params - trainable_params
        }
    
    # Model info
    model_info = get_model_info(model)
    
    # Calculate model size
    size_info = calculate_model_size(model)
    
    # Create dummy input to get output shape
    dummy_input = torch.randn(1, *input_size)
    model.eval()
    with torch.no_grad():
        output = model(dummy_input)
    
    summary = {
        'architecture': str(model),
        'input_shape': input_size,
        'output_shape': output.shape[1:],
        'parameters': model_info,
        'model_size_mb': size_info['size_mb'],
        'layer_count': len(list(model.modules()))
    }
    
    return summary

def plot_class_performance_radar(results, class_names, save_path='./results/plots/radar_chart.png'):
    """Create radar chart for per-class performance"""
    
    import math
    
    # Prepare data
    precision = results['per_class_precision']
    recall = results['per_class_recall']
    f1 = results['per_class_f1']
    
    # Number of classes
    N = len(class_names)
    
    # Compute angles for each class
    angles = [n / float(N) * 2 * math.pi for n in range(N)]
    angles += angles[:1]  # Complete the circle
    
    # Close the radar chart
    precision = list(precision) + [precision[0]]
    recall = list(recall) + [recall[0]]
    f1 = list(f1) + [f1[0]]
    
    # Create radar chart
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    # Plot each metric
    ax.plot(angles, precision, 'o-', linewidth=2, label='Precision', color='blue')
    ax.fill(angles, precision, alpha=0.25, color='blue')
    
    ax.plot(angles, recall, 'o-', linewidth=2, label='Recall', color='red')
    ax.fill(angles, recall, alpha=0.25, color='red')
    
    ax.plot(angles, f1, 'o-', linewidth=2, label='F1-Score', color='green')
    ax.fill(angles, f1, alpha=0.25, color='green')
    
    # Add class labels
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(class_names)
    
    # Set y-axis limits
    ax.set_ylim(0, 1)
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'])
    
    # Add legend and title
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    plt.title('Per-Class Performance Radar Chart', size=16, y=1.08)
    
    plt.tight_layout()
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()

def create_experiment_notebook():
    """Create a Jupyter notebook template for the experiment"""
    
    notebook_content = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "# CIFAR-10 Image Classification Experiment\n",
                    "\n",
                    "This notebook contains the complete pipeline for training and evaluating deep learning models on CIFAR-10 dataset.\n",
                    "\n",
                    "## Objectives\n",
                    "- Compare different CNN architectures\n",
                    "- Analyze model performance\n",
                    "- Generate comprehensive evaluation reports"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Import required libraries\n",
                    "import sys\n",
                    "sys.path.append('./src')\n",
                    "\n",
                    "from data_loader import CIFAR10DataLoader\n",
                    "from models import get_model, count_parameters\n",
                    "from train import Trainer\n",
                    "from evaluate import ModelEvaluator\n",
                    "from utils import *\n",
                    "\n",
                    "import torch\n",
                    "import matplotlib.pyplot as plt\n",
                    "import numpy as np"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Set up experiment parameters\n",
                    "BATCH_SIZE = 32\n",
                    "EPOCHS = 50\n",
                    "LEARNING_RATE = 0.001\n",
                    "MODEL_NAME = 'simple_cnn'  # 'simple_cnn', 'resnet_cifar', 'custom_cnn'\n",
                    "\n",
                    "# Set random seed\n",
                    "set_seed(42)\n",
                    "device = get_device()"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Load data\n",
                    "data_loader = CIFAR10DataLoader(batch_size=BATCH_SIZE)\n",
                    "train_loader, val_loader, test_loader = data_loader.load_data()\n",
                    "\n",
                    "# Visualize sample data\n",
                    "data_loader.show_sample_images()\n",
                    "data_loader.get_class_distribution()"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Create and analyze model\n",
                    "model = get_model(MODEL_NAME)\n",
                    "total_params, trainable_params = count_parameters(model)\n",
                    "\n",
                    "print(f'Model: {MODEL_NAME}')\n",
                    "print(f'Total parameters: {total_params:,}')\n",
                    "print(f'Trainable parameters: {trainable_params:,}')\n",
                    "print(f'Model size: {calculate_model_size(model)[\"size_mb\"]:.2f} MB')"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Train model\n",
                    "trainer = Trainer(model, device)\n",
                    "history = trainer.train_model(\n",
                    "    train_loader, val_loader,\n",
                    "    num_epochs=EPOCHS,\n",
                    "    learning_rate=LEARNING_RATE\n",
                    ")\n",
                    "\n",
                    "# Plot training history\n",
                    "trainer.plot_training_history()"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Evaluate model\n",
                    "evaluator = ModelEvaluator(model, device)\n",
                    "results = evaluator.evaluate_model(test_loader)\n",
                    "\n",
                    "print(f'Test Accuracy: {results[\"overall_accuracy\"]:.4f}')\n",
                    "print(f'Macro F1-Score: {results[\"macro_f1\"]:.4f}')"
                ]
            },
            {
                "cell_type": "code",
                "execution_count": None,
                "metadata": {},
                "source": [
                    "# Generate visualizations\n",
                    "evaluator.plot_confusion_matrix()\n",
                    "evaluator.plot_per_class_metrics(results)\n",
                    "evaluator.plot_prediction_confidence()\n",
                    "evaluator.analyze_misclassifications(test_loader)"
                ]
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "name": "python",
                "version": "3.9.0"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    os.makedirs('./notebooks', exist_ok=True)
    
    with open('./notebooks/cifar10_experiment.ipynb', 'w') as f:
        json.dump(notebook_content, f, indent=2)
    
    print("Experiment notebook created: ./notebooks/cifar10_experiment.ipynb")