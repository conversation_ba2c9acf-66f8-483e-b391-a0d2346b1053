import torch
import torchvision
import torchvision.transforms as transforms
from torch.utils.data import DataLoader, random_split
import matplotlib.pyplot as plt
import numpy as np

class CIFAR10DataLoader:
    def __init__(self, batch_size=32, num_workers=2, validation_split=0.1):
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.validation_split = validation_split
        
        # CIFAR-10 classes
        self.classes = ('airplane', 'automobile', 'bird', 'cat', 'deer', 
                       'dog', 'frog', 'horse', 'ship', 'truck')
        
        # Data transforms
        self.transform_train = transforms.Compose([
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(10),
            transforms.ColorJitter(brightness=0.2, contrast=0.2),
            transforms.ToTensor(),
            transforms.Normalize((0.4914, 0.4822, 0.4465), 
                               (0.2023, 0.1994, 0.2010))
        ])
        
        self.transform_test = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.4914, 0.4822, 0.4465), 
                               (0.2023, 0.1994, 0.2010))
        ])
        
    def load_data(self):
        """Load and split CIFAR-10 dataset"""
        
        # Download and load training data
        train_dataset = torchvision.datasets.CIFAR10(
            root='./data', train=True, download=True, 
            transform=self.transform_train
        )
        
        # Download and load test data
        test_dataset = torchvision.datasets.CIFAR10(
            root='./data', train=False, download=True,
            transform=self.transform_test
        )
        
        # Split training data into train and validation
        train_size = int((1 - self.validation_split) * len(train_dataset))
        val_size = len(train_dataset) - train_size
        
        train_dataset, val_dataset = random_split(
            train_dataset, [train_size, val_size],
            generator=torch.Generator().manual_seed(42)
        )
        
        # Create data loaders
        self.train_loader = DataLoader(
            train_dataset, batch_size=self.batch_size, 
            shuffle=True, num_workers=self.num_workers
        )
        
        self.val_loader = DataLoader(
            val_dataset, batch_size=self.batch_size,
            shuffle=False, num_workers=self.num_workers
        )
        
        self.test_loader = DataLoader(
            test_dataset, batch_size=self.batch_size,
            shuffle=False, num_workers=self.num_workers
        )
        
        return self.train_loader, self.val_loader, self.test_loader
    
    def show_sample_images(self, num_samples=8):
        """Display sample images from dataset"""
        
        # Get a batch of training images
        dataiter = iter(self.train_loader)
        images, labels = next(dataiter)
        
        # Denormalize images for display
        def denormalize(tensor):
            tensor = tensor.clone()
            tensor[0] = tensor[0] * 0.2023 + 0.4914
            tensor[1] = tensor[1] * 0.1994 + 0.4822
            tensor[2] = tensor[2] * 0.2010 + 0.4465
            return torch.clamp(tensor, 0, 1)
        
        # Plot images
        fig, axes = plt.subplots(2, 4, figsize=(12, 6))
        for i in range(num_samples):
            row, col = i // 4, i % 4
            img = denormalize(images[i])
            axes[row, col].imshow(np.transpose(img, (1, 2, 0)))
            axes[row, col].set_title(f'{self.classes[labels[i]]}')
            axes[row, col].axis('off')
        
        plt.tight_layout()
        plt.savefig('./results/plots/sample_images.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def get_class_distribution(self):
        """Analyze class distribution in dataset"""
        
        # Count classes in training set
        class_counts = torch.zeros(10)
        for _, labels in self.train_loader:
            for label in labels:
                class_counts[label] += 1
        
        # Plot distribution
        plt.figure(figsize=(10, 6))
        plt.bar(self.classes, class_counts.numpy())
        plt.title('Class Distribution in Training Set')
        plt.xlabel('Classes')
        plt.ylabel('Number of Samples')
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('./results/plots/class_distribution.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return class_counts