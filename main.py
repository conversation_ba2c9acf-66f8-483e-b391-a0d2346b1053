#!/usr/bin/env python3
"""
CIFAR-10 Image Classification Project
Main execution script for training and evaluating deep learning models
"""

import os
import torch
import numpy as np
import random
import argparse
import json
from datetime import datetime

# Import custom modules
from src.data_loader import CIFAR10DataLoader
from src.models import get_model, count_parameters
from src.train import Trainer
from src.evaluate import ModelEvaluator

def set_seed(seed=42):
    """Set random seeds for reproducibility"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False

def create_directories():
    """Create necessary directories for the project"""
    directories = [
        'data', 'models/checkpoints', 'models/final',
        'results/plots', 'results/logs', 'results/metrics'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def save_experiment_config(config, filepath):
    """Save experiment configuration"""
    with open(filepath, 'w') as f:
        json.dump(config, f, indent=4)

def main():
    parser = argparse.ArgumentParser(description='CIFAR-10 Classification Training')
    parser.add_argument('--model', type=str, default='simple_cnn', 
                       choices=['simple_cnn', 'resnet_cifar', 'custom_cnn'],
                       help='Model architecture to use')
    parser.add_argument('--epochs', type=int, default=50, 
                       help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=32, 
                       help='Batch size for training')
    parser.add_argument('--learning_rate', type=float, default=0.001, 
                       help='Learning rate')
    parser.add_argument('--optimizer', type=str, default='adam', 
                       choices=['adam', 'sgd'], help='Optimizer')
    parser.add_argument('--scheduler', type=str, default='plateau', 
                       choices=['plateau', 'step'], help='LR scheduler')
    parser.add_argument('--seed', type=int, default=42, 
                       help='Random seed')
    parser.add_argument('--evaluate_only', action='store_true', 
                       help='Only evaluate existing model')
    
    args = parser.parse_args()
    
    # Set up experiment
    set_seed(args.seed)
    create_directories()
    
    # Device setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Experiment configuration
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    experiment_name = f"{args.model}_{timestamp}"
    
    config = {
        'model': args.model,
        'epochs': args.epochs,
        'batch_size': args.batch_size,
        'learning_rate': args.learning_rate,
        'optimizer': args.optimizer,
        'scheduler': args.scheduler,
        'seed': args.seed,
        'device': str(device),
        'timestamp': timestamp
    }
    
    save_experiment_config(config, f'./results/logs/{experiment_name}_config.json')
    
    print("="*60)
    print(f"CIFAR-10 Classification Experiment: {experiment_name}")
    print("="*60)
    print(f"Configuration: {config}")
    print("="*60)
    
    # Data loading
    print("\n1. Loading CIFAR-10 dataset...")
    data_loader = CIFAR10DataLoader(
        batch_size=args.batch_size,
        num_workers=4 if device.type == 'cuda' else 2,
        validation_split=0.1
    )
    
    train_loader, val_loader, test_loader = data_loader.load_data()
    
    print(f"Train samples: {len(train_loader.dataset)}")
    print(f"Validation samples: {len(val_loader.dataset)}")
    print(f"Test samples: {len(test_loader.dataset)}")
    
    # Show sample images and class distribution
    data_loader.show_sample_images()
    data_loader.get_class_distribution()
    
    # Model setup
    print(f"\n2. Setting up {args.model} model...")
    model = get_model(args.model, num_classes=10)
    
    total_params, trainable_params = count_parameters(model)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    # Model summary
    print(f"\nModel Architecture:")
    print(model)
    
    if not args.evaluate_only:
        # Training
        print(f"\n3. Training model...")
        trainer = Trainer(model, device)
        
        history = trainer.train_model(
            train_loader=train_loader,
            val_loader=val_loader,
            num_epochs=args.epochs,
            learning_rate=args.learning_rate,
            optimizer_name=args.optimizer,
            scheduler_type=args.scheduler,
            save_best=True
        )
        
        # Save model and training history
        model_path = f'./models/final/{experiment_name}.pth'
        trainer.save_model(model_path)
        
        # Plot training history
        trainer.plot_training_history(
            save_path=f'./results/plots/{experiment_name}_training_history.png'
        )
        
        # Save training history
        with open(f'./results/logs/{experiment_name}_history.json', 'w') as f:
            json.dump(history, f, indent=4)
    
    else:
        print("\n3. Loading pre-trained model for evaluation...")
        # Load existing model (you would specify the path)
        model_path = f'./models/final/{args.model}_latest.pth'
        if os.path.exists(model_path):
            trainer = Trainer(model, device)
            trainer.load_model(model_path)
        else:
            print(f"Model not found: {model_path}")
            return
    
    # Evaluation
    print(f"\n4. Evaluating model on test set...")
    evaluator = ModelEvaluator(model, device)
    results = evaluator.evaluate_model(test_loader)
    
    # Print results
    print(f"\nTest Results:")
    print(f"Overall Accuracy: {results['overall_accuracy']:.4f}")
    print(f"Macro F1-Score: {results['macro_f1']:.4f}")
    print(f"Weighted F1-Score: {results['weighted_f1']:.4f}")
    
    # Generate comprehensive visualizations
    print(f"\n5. Generating evaluation visualizations...")
    
    # Confusion matrix
    cm = evaluator.plot_confusion_matrix(
        save_path=f'./results/plots/{experiment_name}_confusion_matrix.png'
    )
    
    # Per-class metrics
    evaluator.plot_per_class_metrics(
        results, save_path=f'./results/plots/{experiment_name}_per_class_metrics.png'
    )
    
    # Classification report
    report_df = evaluator.plot_classification_report(
        results, save_path=f'./results/plots/{experiment_name}_classification_report.png'
    )
    
    # Prediction confidence analysis
    evaluator.plot_prediction_confidence(
        save_path=f'./results/plots/{experiment_name}_prediction_confidence.png'
    )
    
    # Misclassification analysis
    evaluator.analyze_misclassifications(
        test_loader, save_path=f'./results/plots/{experiment_name}_misclassifications.png'
    )
    
    # Generate comprehensive report
    report_path = evaluator.generate_evaluation_report(
        results, save_path=f'./results/{experiment_name}_evaluation_report.txt'
    )
    
    # Save results to JSON
    results_json = {
        'experiment_name': experiment_name,
        'config': config,
        'results': {
            'overall_accuracy': float(results['overall_accuracy']),
            'macro_precision': float(results['macro_precision']),
            'macro_recall': float(results['macro_recall']),
            'macro_f1': float(results['macro_f1']),
            'weighted_precision': float(results['weighted_precision']),
            'weighted_recall': float(results['weighted_recall']),
            'weighted_f1': float(results['weighted_f1']),
            'per_class_f1': [float(x) for x in results['per_class_f1']],
            'per_class_precision': [float(x) for x in results['per_class_precision']],
            'per_class_recall': [float(x) for x in results['per_class_recall']]
        }
    }
    
    with open(f'./results/metrics/{experiment_name}_results.json', 'w') as f:
        json.dump(results_json, f, indent=4)
    
    print(f"\n6. Experiment completed successfully!")
    print(f"Results saved to: ./results/{experiment_name}_*")
    print(f"Model saved to: {model_path}")
    print("="*60)

if __name__ == "__main__":
    main()