import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
import os

class ModelEvaluator:
    def __init__(self, model, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.class_names = ['airplane', 'automobile', 'bird', 'cat', 'deer', 
                           'dog', 'frog', 'horse', 'ship', 'truck']
    
    def evaluate_model(self, test_loader):
        """
        Comprehensive model evaluation
        """
        self.model.eval()
        all_predictions = []
        all_labels = []
        all_probabilities = []
        
        with torch.no_grad():
            for inputs, labels in test_loader:
                inputs = inputs.to(self.device)
                labels = labels.to(self.device)
                
                outputs = self.model(inputs)
                probabilities = torch.softmax(outputs, dim=1)
                _, predictions = torch.max(outputs, 1)
                
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
        
        self.predictions = np.array(all_predictions)
        self.true_labels = np.array(all_labels)
        self.probabilities = np.array(all_probabilities)
        
        # Calculate metrics
        results = self._calculate_metrics()
        
        return results
    
    def _calculate_metrics(self):
        """Calculate various performance metrics"""
        
        # Overall accuracy
        accuracy = accuracy_score(self.true_labels, self.predictions)
        
        # Per-class metrics
        precision, recall, f1, support = precision_recall_fscore_support(
            self.true_labels, self.predictions, average=None
        )
        
        # Macro and weighted averages
        macro_precision, macro_recall, macro_f1, _ = precision_recall_fscore_support(
            self.true_labels, self.predictions, average='macro'
        )
        
        weighted_precision, weighted_recall, weighted_f1, _ = precision_recall_fscore_support(
            self.true_labels, self.predictions, average='weighted'
        )
        
        results = {
            'overall_accuracy': accuracy,
            'per_class_precision': precision,
            'per_class_recall': recall,
            'per_class_f1': f1,
            'per_class_support': support,
            'macro_precision': macro_precision,
            'macro_recall': macro_recall,
            'macro_f1': macro_f1,
            'weighted_precision': weighted_precision,
            'weighted_recall': weighted_recall,
            'weighted_f1': weighted_f1
        }
        
        return results
    
    def plot_confusion_matrix(self, save_path='./results/plots/confusion_matrix.png'):
        """Plot confusion matrix"""
        
        cm = confusion_matrix(self.true_labels, self.predictions)
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=self.class_names, yticklabels=self.class_names)
        plt.title('Confusion Matrix')
        plt.xlabel('Predicted Label')
        plt.ylabel('True Label')
        plt.tight_layout()
        
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        return cm
    
    def plot_per_class_metrics(self, results, save_path='./results/plots/per_class_metrics.png'):
        """Plot per-class precision, recall, and F1-score"""
        
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6))
        
        x_pos = np.arange(len(self.class_names))
        
        # Precision
        ax1.bar(x_pos, results['per_class_precision'], color='skyblue')
        ax1.set_title('Per-Class Precision')
        ax1.set_xlabel('Classes')
        ax1.set_ylabel('Precision')
        ax1.set_xticks(x_pos)
        ax1.set_xticklabels(self.class_names, rotation=45)
        ax1.set_ylim(0, 1)
        
        # Add value labels on bars
        for i, v in enumerate(results['per_class_precision']):
            ax1.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
        
        # Recall
        ax2.bar(x_pos, results['per_class_recall'], color='lightcoral')
        ax2.set_title('Per-Class Recall')
        ax2.set_xlabel('Classes')
        ax2.set_ylabel('Recall')
        ax2.set_xticks(x_pos)
        ax2.set_xticklabels(self.class_names, rotation=45)
        ax2.set_ylim(0, 1)
        
        for i, v in enumerate(results['per_class_recall']):
            ax2.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
        
        # F1-Score
        ax3.bar(x_pos, results['per_class_f1'], color='lightgreen')
        ax3.set_title('Per-Class F1-Score')
        ax3.set_xlabel('Classes')
        ax3.set_ylabel('F1-Score')
        ax3.set_xticks(x_pos)
        ax3.set_xticklabels(self.class_names, rotation=45)
        ax3.set_ylim(0, 1)
        
        for i, v in enumerate(results['per_class_f1']):
            ax3.text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom')
        
        plt.tight_layout()
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_classification_report(self, results, save_path='./results/plots/classification_report.png'):
        """Plot classification report as heatmap"""
        
        # Create classification report data
        report_data = []
        for i, class_name in enumerate(self.class_names):
            report_data.append([
                results['per_class_precision'][i],
                results['per_class_recall'][i],
                results['per_class_f1'][i],
                results['per_class_support'][i]
            ])
        
        # Add macro and weighted averages
        report_data.append([
            results['macro_precision'],
            results['macro_recall'],
            results['macro_f1'],
            np.sum(results['per_class_support'])
        ])
        
        report_data.append([
            results['weighted_precision'],
            results['weighted_recall'],
            results['weighted_f1'],
            np.sum(results['per_class_support'])
        ])
        
        # Create DataFrame for visualization
        import pandas as pd
        
        labels = self.class_names + ['macro avg', 'weighted avg']
        columns = ['Precision', 'Recall', 'F1-Score', 'Support']
        
        df = pd.DataFrame(report_data, index=labels, columns=columns)
        
        # Plot heatmap (excluding support column for color mapping)
        plt.figure(figsize=(8, 12))
        sns.heatmap(df.iloc[:, :3], annot=True, fmt='.3f', cmap='RdYlBu_r',
                   cbar_kws={'label': 'Score'})
        plt.title('Classification Report')
        plt.tight_layout()
        
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        return df
    
    def plot_prediction_confidence(self, save_path='./results/plots/prediction_confidence.png'):
        """Plot prediction confidence distribution"""
        
        # Get confidence scores (max probability for each prediction)
        confidence_scores = np.max(self.probabilities, axis=1)
        
        # Separate correct and incorrect predictions
        correct_mask = (self.predictions == self.true_labels)
        correct_confidence = confidence_scores[correct_mask]
        incorrect_confidence = confidence_scores[~correct_mask]
        
        plt.figure(figsize=(12, 5))
        
        # Confidence distribution
        plt.subplot(1, 2, 1)
        plt.hist(correct_confidence, bins=50, alpha=0.7, label='Correct Predictions', color='green')
        plt.hist(incorrect_confidence, bins=50, alpha=0.7, label='Incorrect Predictions', color='red')
        plt.xlabel('Confidence Score')
        plt.ylabel('Frequency')
        plt.title('Prediction Confidence Distribution')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Confidence vs Accuracy
        plt.subplot(1, 2, 2)
        bins = np.linspace(0, 1, 21)
        bin_centers = (bins[:-1] + bins[1:]) / 2
        bin_accuracies = []
        
        for i in range(len(bins)-1):
            mask = (confidence_scores >= bins[i]) & (confidence_scores < bins[i+1])
            if np.sum(mask) > 0:
                accuracy = np.mean(correct_mask[mask])
                bin_accuracies.append(accuracy)
            else:
                bin_accuracies.append(0)
        
        plt.plot(bin_centers, bin_accuracies, 'bo-', label='Actual')
        plt.plot([0, 1], [0, 1], 'r--', label='Perfect Calibration')
        plt.xlabel('Confidence Score')
        plt.ylabel('Accuracy')
        plt.title('Reliability Diagram')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def analyze_misclassifications(self, data_loader, save_path='./results/plots/misclassifications.png'):
        """Analyze and visualize misclassified examples"""
        
        self.model.eval()
        misclassified_images = []
        misclassified_true = []
        misclassified_pred = []
        misclassified_conf = []
        
        with torch.no_grad():
            for inputs, labels in data_loader:
                inputs = inputs.to(self.device)
                labels = labels.to(self.device)
                
                outputs = self.model(inputs)
                probabilities = torch.softmax(outputs, dim=1)
                _, predictions = torch.max(outputs, 1)
                
                # Find misclassified examples
                misclassified_mask = predictions != labels
                
                if torch.any(misclassified_mask):
                    misclassified_images.extend(inputs[misclassified_mask].cpu())
                    misclassified_true.extend(labels[misclassified_mask].cpu())
                    misclassified_pred.extend(predictions[misclassified_mask].cpu())
                    
                    # Get confidence for wrong predictions
                    wrong_probs = probabilities[misclassified_mask]
                    wrong_conf = torch.max(wrong_probs, dim=1)[0]
                    misclassified_conf.extend(wrong_conf.cpu())
                
                # Stop after collecting enough examples
                if len(misclassified_images) >= 16:
                    break
        
        # Plot misclassified examples
        if len(misclassified_images) > 0:
            fig, axes = plt.subplots(4, 4, figsize=(12, 12))
            
            for i in range(min(16, len(misclassified_images))):
                row, col = i // 4, i % 4
                
                # Denormalize image for display
                img = misclassified_images[i]
                img = img * torch.tensor([0.2023, 0.1994, 0.2010]).view(3, 1, 1)
                img = img + torch.tensor([0.4914, 0.4822, 0.4465]).view(3, 1, 1)
                img = torch.clamp(img, 0, 1)
                
                axes[row, col].imshow(np.transpose(img, (1, 2, 0)))
                axes[row, col].set_title(
                    f'True: {self.class_names[misclassified_true[i]]}\n'
                    f'Pred: {self.class_names[misclassified_pred[i]]}\n'
                    f'Conf: {misclassified_conf[i]:.3f}',
                    fontsize=8
                )
                axes[row, col].axis('off')
            
            plt.suptitle('Misclassified Examples', fontsize=16)
            plt.tight_layout()
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.show()
    
    def generate_evaluation_report(self, results, save_path='./results/evaluation_report.txt'):
        """Generate comprehensive text evaluation report"""
        
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        with open(save_path, 'w') as f:
            f.write("="*60 + "\n")
            f.write("CIFAR-10 MODEL EVALUATION REPORT\n")
            f.write("="*60 + "\n\n")
            
            # Overall metrics
            f.write("OVERALL PERFORMANCE:\n")
            f.write("-"*30 + "\n")
            f.write(f"Overall Accuracy: {results['overall_accuracy']:.4f}\n")
            f.write(f"Macro Precision: {results['macro_precision']:.4f}\n")
            f.write(f"Macro Recall: {results['macro_recall']:.4f}\n")
            f.write(f"Macro F1-Score: {results['macro_f1']:.4f}\n")
            f.write(f"Weighted Precision: {results['weighted_precision']:.4f}\n")
            f.write(f"Weighted Recall: {results['weighted_recall']:.4f}\n")
            f.write(f"Weighted F1-Score: {results['weighted_f1']:.4f}\n\n")
            
            # Per-class metrics
            f.write("PER-CLASS PERFORMANCE:\n")
            f.write("-"*30 + "\n")
            f.write(f"{'Class':<12} {'Precision':<10} {'Recall':<10} {'F1-Score':<10} {'Support':<10}\n")
            f.write("-"*60 + "\n")
            
            for i, class_name in enumerate(self.class_names):
                f.write(f"{class_name:<12} {results['per_class_precision'][i]:<10.3f} "
                       f"{results['per_class_recall'][i]:<10.3f} "
                       f"{results['per_class_f1'][i]:<10.3f} "
                       f"{results['per_class_support'][i]:<10}\n")
            
            # Best and worst performing classes
            f.write(f"\nBEST PERFORMING CLASSES (by F1-score):\n")
            f.write("-"*40 + "\n")
            best_indices = np.argsort(results['per_class_f1'])[::-1][:3]
            for idx in best_indices:
                f.write(f"{self.class_names[idx]}: {results['per_class_f1'][idx]:.3f}\n")
            
            f.write(f"\nWORST PERFORMING CLASSES (by F1-score):\n")
            f.write("-"*40 + "\n")
            worst_indices = np.argsort(results['per_class_f1'])[:3]
            for idx in worst_indices:
                f.write(f"{self.class_names[idx]}: {results['per_class_f1'][idx]:.3f}\n")
        
        print(f"Evaluation report saved to {save_path}")
        return save_path